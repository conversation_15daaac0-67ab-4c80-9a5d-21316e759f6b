namespace RealEstate.Application.DTO
{
    public class InvoiceStatsDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal PendingAmount { get; set; }
        public decimal CompletedAmount { get; set; }
        public decimal FailedAmount { get; set; }
        public int TotalInvoices { get; set; }
        public int PendingInvoices { get; set; }
        public int CompletedInvoices { get; set; }
        public int FailedInvoices { get; set; }
        public int CancelledInvoices { get; set; }
        public DateTimeOffset? LastInvoiceDate { get; set; }
        public Dictionary<string, decimal> SpentByType { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, int> InvoiceCountByType { get; set; } = new Dictionary<string, int>();
    }
}
