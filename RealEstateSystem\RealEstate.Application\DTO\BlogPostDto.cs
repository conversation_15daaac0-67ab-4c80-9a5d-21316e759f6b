﻿namespace RealEstate.Application.DTO
{
    public class BlogPostDto
    {
        public Guid Id { get; set; }
        public required Guid AuthorID { get; set; }
        public string? AuthorName { get; set; } // Assuming you want to include the author's name as part of the DTO
        public required string Title { get; set; }
        public required string Slug { get; set; }
        public required string Content { get; set; }
        public string? FeaturedImage { get; set; }
        public string? Tags { get; set; }
        public string? Status { get; set; }
        public bool IsFeature { get; set; }
        public DateTimeOffset? PublishedAt { get; set; }
        public IEnumerable<BlogCommentDto>? BlogComments { get; set; } // Including comments as a collection of DTOs
    }

}
