using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class PropertyPerformanceDto
    {
        public int TotalProperties { get; set; }
        public List<PropertyPerformanceDetailDto> PropertiesPerformance { get; set; } = new List<PropertyPerformanceDetailDto>();
        public BestPerformingPropertyDto BestPerforming { get; set; }
        public List<PropertyAttentionDto> NeedsAttention { get; set; } = new List<PropertyAttentionDto>();
    }

    public class PropertyPerformanceDetailDto
    {
        public Guid PropertyId { get; set; }
        public string PropertyName { get; set; }
        public int Views { get; set; }
        public int Favorites { get; set; }
        public int ContactRequests { get; set; }
        public PropertyStatus Status { get; set; }
        public DateTimeOffset ExpiresAt { get; set; }
        public decimal Rating { get; set; }
        public int ReviewCount { get; set; }
    }

    public class BestPerformingPropertyDto
    {
        public Guid PropertyId { get; set; }
        public string PropertyName { get; set; }
    }

    public class PropertyAttentionDto
    {
        public Guid PropertyId { get; set; }
        public string PropertyName { get; set; }
        public string Issue { get; set; }
        public DateTimeOffset? ExpiresAt { get; set; }
    }
} 