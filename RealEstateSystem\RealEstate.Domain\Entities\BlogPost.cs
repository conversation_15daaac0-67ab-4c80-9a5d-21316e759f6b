using System.Text;
using System.Xml.Linq;

namespace RealEstate.Domain.Entities
{
    public class BlogPost : BaseEntityWithAuditable
    {
        public required Guid AuthorID { get; set; }
        public required string Title { get; set; }
        public required string Content { get; set; }
        public string? FeaturedImage { get; set; }
        public string? Tags { get; set; }
        public string? Status { get; set; }
        public required string Slug { get; set; }
        public bool IsFeature { get; set; }
        public DateTimeOffset? PublishedAt { get; set; }       

        public AppUser? Author { get; set; }
        public ICollection<BlogComment>? BlogComments { get; set; }
    }
}