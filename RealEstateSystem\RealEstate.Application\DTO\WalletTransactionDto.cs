﻿using RealEstate.Domain.Common;

namespace RealEstate.Application.DTO
{
    public class WalletTransactionDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public decimal Amount { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTimeOffset CreatedAt { get; set; }
        public string Status { get; set; } = EnumValues.TransactionStatus.PENDING.ToString();
        public string? PaymentMethod { get; set; }
        public string TransactionReference { get; set; } = string.Empty;
        public DateTimeOffset? ProcessedAt { get; set; }
        public string? FailureReason { get; set; }
    }
}
