using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Domain.Entities
{
    public class AppUser : BaseEntityWithAuditable
    {
        public required string FullName { get; set; }
        public required string Email { get; set; }
        public required string PasswordHash { get; set; }
        public required string PasswordSalt { get; set; }
        public UserType UserType { get; set; }
        public required string Phone { get; set; }
        public string? Phone2 { get; set; }
        public string? Phone3 { get; set; }
        public string? AvatarImage { get; set; }
        public DateTimeOffset? LastLogin { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal TotalSpent { get; set; } = 0;

        [Required]
        public string MemberRank { get; set; } = "default";

        public string? TransferCode { get; set; }

        public string? PersonalTaxCode { get; set; }

        // Invoice Information
        public string? InvoiceBuyerName { get; set; }
        public string? InvoiceEmail { get; set; }
        public string? InvoiceCompanyName { get; set; }
        public string? InvoiceTaxCode { get; set; }
        public string? InvoiceAddress { get; set; }

        public bool IsActive { get; set; } = true;

        public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public ICollection<Property> Properties { get; set; } = new List<Property>();
        public ICollection<BlogPost> BlogPosts { get; set; } = new List<BlogPost>();
        public ICollection<BlogComment> BlogComments { get; set; } = new List<BlogComment>();
        public ICollection<UserFavorite> UserFavorites { get; set; } = new List<UserFavorite>();
    }
}