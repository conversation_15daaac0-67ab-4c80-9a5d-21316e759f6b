﻿using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class UserAminDto
    {
        public Guid Id { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public UserType UserType { get; set; }
        public string? Phone { get; set; }

        public DateTimeOffset? LastLogin { get; set; }
        public string? AvatarImage { get; set; }
        public string? AvatarURL { get; set; }
        public IEnumerable<string>? Roles { get; set; }
        public IEnumerable<string>? Permissions { get; set; }
    }
}
