﻿using Microsoft.AspNetCore.Http;

namespace RealEstate.Application.DTO
{
    public class PropertyMediaDto
    {
        public Guid Id { get; set; }
        public Guid? PropertyID { get; set; }
        public string? MediaType { get; set; }
        public string? MediaURL { get; set; }
        public string? ThumbnailURL { get; set; }
        public string? SmallURL { get; set; }
        public string? MediumURL { get; set; }
        public string? LargeURL { get; set; }
        public string? FilePath { get; set; }
        public DateTimeOffset UploadedAt { get; set; }
        public string? Caption { get; set; }
        public bool IsAvatar { get; set; }
    }

    public class PropertyMediaUpload
    {
        public Guid? propertyId { get; set; } 
        public IFormFileCollection files { get; set; }
    }

    public class PropertyMediaDtoResponse
    {
        public Guid Id { get; set; }
        public string? MediaURL { get; set; }
        public string? Caption { get; set; }
        public bool IsAvatar { get; set; }
    }
}
