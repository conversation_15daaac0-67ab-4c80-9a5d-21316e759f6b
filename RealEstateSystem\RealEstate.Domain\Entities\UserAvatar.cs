using System;

namespace RealEstate.Domain.Entities
{
    public class UserAvatar : BaseEntity
    {
        public Guid UserID { get; set; }
        public AppUser User { get; set; }
        public string MediaType { get; set; }
        public string MediaURL { get; set; }
        public string? FilePath { get; set; }
        public string? ThumbnailPath { get; set; }
        public string? SmallPath { get; set; }
        public string? MediumPath { get; set; }
        public string? LargePath { get; set; }
        public DateTimeOffset UploadedAt { get; set; }
        public string? Caption { get; set; }
    }
}
