﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Domain.Entities
{
    public class Notification : BaseEntity
    {
        [ForeignKey("User")]
        public Guid? UserId { get; set; }

        [Required]
        public NotificationType Type { get; set; }

        public NotificationCategory Category { get; set; }

        [Required]
        public string Title { get; set; }

        [Required]
        public string Message { get; set; }

        public bool IsRead { get; set; } = false;

        public DateTimeOffset CreatedAt { get; set; } = DateTime.UtcNow;

        // Additional fields for actionable notifications
        public Guid? RelatedEntityId { get; set; } // Contact request ID, transaction ID, etc.

        public Guid? RelatedPropertyId { get; set; } // Property ID for property-related notifications

        [MaxLength(500)]
        public string? ActionUrl { get; set; } // URL for direct navigation

        // Navigation property
        public virtual AppUser? User { get; set; }
    }
}
