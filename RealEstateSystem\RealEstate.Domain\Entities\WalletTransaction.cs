﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RealEstate.Domain.Common;

namespace RealEstate.Domain.Entities
{
    public class WalletTransaction : BaseEntity
    {

        [ForeignKey("AppUser")]
        public Guid UserId { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal Amount { get; set; }

        [Required]
        public string Type { get; set; } 

        [Required]
        public string Description { get; set; }

        public DateTimeOffset CreatedAt { get; set; } = DateTime.UtcNow;

        [Required]
        public string Status { get; set; } = EnumValues.TransactionStatus.PENDING.ToString();

        [Required]
        public string PaymentMethod { get; set; } = string.Empty;

        [Required]
        public string TransactionReference { get; set; } = string.Empty;

        public string? ExternalPaymentReference { get; set; }

        public DateTimeOffset? ProcessedAt { get; set; }

        public string? FailureReason { get; set; }

        public virtual AppUser User { get; set; }
    }
}
