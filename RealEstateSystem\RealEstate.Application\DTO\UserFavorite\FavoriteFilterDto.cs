namespace RealEstate.Application.DTO.UserFavorite
{
    public class FavoriteFilterDto
    {
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTimeOffset? FromDate { get; set; }
        public DateTimeOffset? ToDate { get; set; }
        public string? SortBy { get; set; } = "CreatedAt"; // CreatedAt, Price
        public bool SortDescending { get; set; } = true;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
}
